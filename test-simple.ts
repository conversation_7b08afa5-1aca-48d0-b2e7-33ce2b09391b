// Simple test to verify the Mermaid renderer compiles correctly
import { renderMermaidToSVG } from "./src/mermaid-renderer.ts";

// Test with the diagram from README.md
const testDiagram = `
graph TD
    A[Start] --> B[Process]
    B --> C[End]
`;

console.log("Testing Mermaid renderer...");

try {
  const result = renderMermaidToSVG(testDiagram);
  console.log("Success! Result length:", result.length);
  console.log("Contains SVG:", result.includes("<svg"));
} catch (error) {
  console.log("Error:", error);
}
