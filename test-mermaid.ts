#!/usr/bin/env -S deno run --allow-net

// Test script to verify the new Mermaid renderer works correctly
import { renderMermaidToSVG, isValidMermaidSyntax, getMermaidInfo } from "./src/mermaid-renderer.ts";

// Test diagram from README.md
const testDiagram = `
graph TD
    A[Start] --> B[Process]
    B --> C[End]
`;

console.log("Testing Mermaid renderer with @rendermaid/core...\n");

// Test 1: Basic rendering
console.log("1. Testing basic rendering:");
try {
  const result = renderMermaidToSVG(testDiagram);
  console.log("✅ Rendering successful");
  console.log("Result type:", typeof result);
  console.log("Contains SVG:", result.includes("<svg"));
  console.log("Contains mermaid-diagram class:", result.includes('class="mermaid-diagram"'));
  console.log("First 200 chars:", result.substring(0, 200) + "...\n");
} catch (error) {
  console.log("❌ Rendering failed:", error);
}

// Test 2: Validation
console.log("2. Testing validation:");
try {
  const isValid = isValidMermaidSyntax(testDiagram);
  console.log("✅ Validation successful");
  console.log("Is valid:", isValid);
} catch (error) {
  console.log("❌ Validation failed:", error);
}

// Test 3: Info extraction
console.log("\n3. Testing info extraction:");
try {
  const info = getMermaidInfo(testDiagram);
  console.log("✅ Info extraction successful");
  console.log("Info:", info);
} catch (error) {
  console.log("❌ Info extraction failed:", error);
}

// Test 4: Error handling
console.log("\n4. Testing error handling:");
try {
  const invalidDiagram = "invalid mermaid syntax";
  const result = renderMermaidToSVG(invalidDiagram);
  console.log("✅ Error handling successful");
  console.log("Contains error div:", result.includes('class="mermaid-error"'));
  console.log("Error result:", result.substring(0, 200) + "...");
} catch (error) {
  console.log("❌ Error handling failed:", error);
}

console.log("\nTest completed!");
