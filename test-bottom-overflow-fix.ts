#!/usr/bin/env -S deno run --allow-net

// Test script to verify bottom overflow fix in @rendermaid/core v0.6.0
import { 
  renderMermaidToSVG, 
  renderMermaidWithConfig,
  getMermaidInfo 
} from "./src/mermaid-renderer.ts";

console.log("🔧 Testing bottom overflow fix for Mermaid diagrams...\n");

// Test 1: Simple diagram with default configuration
console.log("1. Testing simple diagram with enhanced configuration:");
const simpleDiagram = `
flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process]
    B -->|No| D[Skip]
    C --> E[End]
    D --> E
`;

try {
  const result1 = renderMermaidToSVG(simpleDiagram);
  const info1 = getMermaidInfo(simpleDiagram);
  
  console.log("✅ Simple diagram rendering:");
  console.log("   Contains SVG:", result1.includes("<svg"));
  console.log("   Diagram depth:", info1.analysis?.depth);
  console.log("   Complexity:", info1.analysis?.complexity);
  
  // Extract SVG dimensions
  const heightMatch = result1.match(/height="(\d+)"/);
  const height = heightMatch ? parseInt(heightMatch[1]) : 0;
  console.log("   SVG height:", height, "px");
  console.log("   Height increased from default 600px:", height > 600);
} catch (error) {
  console.log("❌ Simple diagram error:", error);
}

// Test 2: Deep diagram that would cause bottom overflow
console.log("\n2. Testing deep diagram prone to bottom overflow:");
const deepDiagram = `
flowchart TD
    A[Start] --> B[Step 1]
    B --> C[Step 2]
    C --> D[Step 3]
    D --> E[Step 4]
    E --> F[Step 5]
    F --> G[Step 6]
    G --> H[Step 7]
    H --> I[Step 8]
    I --> J[Step 9]
    J --> K[Final Step]
    K --> L[End]
`;

try {
  const result2 = renderMermaidToSVG(deepDiagram);
  const info2 = getMermaidInfo(deepDiagram);
  
  console.log("✅ Deep diagram rendering:");
  console.log("   Contains SVG:", result2.includes("<svg"));
  console.log("   Diagram depth:", info2.analysis?.depth);
  console.log("   Complexity:", info2.analysis?.complexity);
  
  // Extract SVG dimensions
  const heightMatch = result2.match(/height="(\d+)"/);
  const height = heightMatch ? parseInt(heightMatch[1]) : 0;
  console.log("   SVG height:", height, "px");
  console.log("   Height significantly increased:", height > 800);
} catch (error) {
  console.log("❌ Deep diagram error:", error);
}

// Test 3: Complex diagram with many nodes and edges
console.log("\n3. Testing complex diagram with many elements:");
const complexDiagram = `
flowchart TD
    A[User Request] --> B{Authentication}
    B -->|Valid| C[Load Dashboard]
    B -->|Invalid| D[Show Login]
    C --> E{Has Permissions}
    E -->|Admin| F[Admin Panel]
    E -->|User| G[User Panel]
    E -->|Guest| H[Limited Access]
    F --> I[Manage Users]
    F --> J[System Settings]
    G --> K[View Data]
    G --> L[Edit Profile]
    H --> M[View Public Data]
    I --> N[User Database]
    J --> O[Config Files]
    K --> P[Data Visualization]
    L --> Q[Profile Database]
    M --> R[Public API]
    N --> S[Audit Log]
    O --> S
    P --> S
    Q --> S
    R --> S
    S --> T[End]
    D --> U[Validate Credentials]
    U -->|Success| C
    U -->|Failure| V[Show Error]
    V --> D
    
    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#fce4ec
    style V fill:#ffebee
`;

try {
  const result3 = renderMermaidToSVG(complexDiagram);
  const info3 = getMermaidInfo(complexDiagram);
  
  console.log("✅ Complex diagram rendering:");
  console.log("   Contains SVG:", result3.includes("<svg"));
  console.log("   Diagram depth:", info3.analysis?.depth);
  console.log("   Complexity:", info3.analysis?.complexity);
  console.log("   Complex diagram optimization triggered:", (info3.analysis?.complexity || 0) > 20);
  
  // Extract SVG dimensions
  const heightMatch = result3.match(/height="(\d+)"/);
  const widthMatch = result3.match(/width="(\d+)"/);
  const height = heightMatch ? parseInt(heightMatch[1]) : 0;
  const width = widthMatch ? parseInt(widthMatch[1]) : 0;
  console.log("   SVG dimensions:", width, "x", height, "px");
  console.log("   Minimum size for complex diagrams:", height >= 1000 && width >= 1000);
} catch (error) {
  console.log("❌ Complex diagram error:", error);
}

// Test 4: Custom configuration override
console.log("\n4. Testing custom configuration override:");
try {
  const customResult = renderMermaidWithConfig(simpleDiagram, {
    height: 1200,
    width: 1000,
    theme: "dark"
  });
  
  console.log("✅ Custom configuration working:");
  console.log("   Contains SVG:", customResult.includes("<svg"));
  
  // Extract SVG dimensions
  const heightMatch = customResult.match(/height="(\d+)"/);
  const widthMatch = customResult.match(/width="(\d+)"/);
  const height = heightMatch ? parseInt(heightMatch[1]) : 0;
  const width = widthMatch ? parseInt(widthMatch[1]) : 0;
  console.log("   Custom dimensions applied:", width, "x", height, "px");
  console.log("   Custom height respected:", height === 1200);
  console.log("   Custom width respected:", width === 1000);
} catch (error) {
  console.log("❌ Custom configuration error:", error);
}

console.log("\n🎉 Bottom overflow fix testing completed!");
console.log("\nSummary:");
console.log("- Default height increased from 600px to 800px");
console.log("- Dynamic height calculation based on diagram depth");
console.log("- Complex diagrams get minimum 1000x1000px canvas");
console.log("- Custom configuration override available");
console.log("- Generous bottom padding (120px) to prevent content clipping");
