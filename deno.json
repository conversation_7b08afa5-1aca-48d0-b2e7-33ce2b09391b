{"tasks": {"start": "deno run --allow-net --allow-read --allow-env --allow-write app.tsx", "dev": "deno run --allow-net --allow-read --allow-env --allow-write --watch app.tsx", "setup": "mkdir -p public/css public/js content/posts && curl -sL https://unpkg.com/htmx.org/dist/htmx.min.js -o public/js/htmx.min.js"}, "fmt": {"options": {"indentWidth": 2, "lineWidth": 80, "singleQuote": false}}, "lint": {"include": ["src/", "app.tsx"]}, "imports": {"mono-jsx": "npm:mono-jsx@^0.6.6"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "mono-jsx"}, "deploy": {"exclude": ["**/node_modules"], "include": [], "entrypoint": "app.tsx"}}