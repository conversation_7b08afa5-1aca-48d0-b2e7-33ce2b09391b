{"tasks": {"start": "deno run --allow-net --allow-read --allow-env --allow-write app.tsx", "dev": "deno run --allow-net --allow-read --allow-env --allow-write --watch app.tsx", "setup": "mkdir -p public/css public/js content/posts && curl -sL https://unpkg.com/htmx.org/dist/htmx.min.js -o public/js/htmx.min.js"}, "fmt": {"options": {"indentWidth": 2, "lineWidth": 80, "singleQuote": false}}, "lint": {"include": ["src/", "app.tsx"]}, "imports": {"mono-jsx": "npm:mono-jsx@^0.6.7", "@rendermaid/core": "jsr:@rendermaid/core@^0.4.0", "ts-pattern": "npm:ts-pattern@^5.7.1", "marked": "https://esm.sh/marked@15.0.12", "highlight.js": "https://esm.sh/highlight.js@11.11.1", "@std/yaml": "jsr:@std/yaml@^1.0.8"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "mono-jsx"}, "deploy": {"exclude": ["**/node_modules"], "include": [], "entrypoint": "app.tsx"}}